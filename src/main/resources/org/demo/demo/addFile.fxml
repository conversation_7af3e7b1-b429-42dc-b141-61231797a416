<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<?import javafx.geometry.Insets?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<BorderPane xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.AddFileController"
            stylesheets="@css/addfile.css">

  <top>
    <fx:include source="navbar.fxml"/>
  </top>

  <center>
    <VBox styleClass="page-content" spacing="25" alignment="CENTER">
      <padding>
        <Insets top="30" right="30" bottom="30" left="30"/>
      </padding>

      <Label fx:id="Text" text="Ajoutez un fichier à votre base de données" styleClass="header-label"/>

      <HBox spacing="10" alignment="CENTER">
        <TextField fx:id="filePathField" promptText="Chemin du fichier" editable="false" styleClass="file-path-field"/>
        <Button onAction="#onDownloadButtonClick" styleClass="icon-button">
          <tooltip>
            <Tooltip text="Télécharger un fichier"/>
          </tooltip>
          <graphic>
            <FontIcon iconLiteral="bi-upload" styleClass="upload-icon"/>
          </graphic>
        </Button>
      </HBox>

      <Button text="Ajouter" onAction="#onAddButtonClick" styleClass="main-button"/>

      <!-- Spinner de chargement -->
      <FontIcon fx:id="loadingSpinner"
                iconLiteral="fa-spinner"
                styleClass="loading-spinner"
                visible="false"
                rotate="0"/>
    </VBox>
  </center>

</BorderPane>
