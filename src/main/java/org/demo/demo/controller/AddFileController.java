package org.demo.demo.controller;

import javafx.animation.RotateTransition;
import javafx.application.Platform;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.stage.FileChooser;
import javafx.util.Duration;
import org.demo.demo.services.AddFileService;
import org.kordamp.ikonli.javafx.FontIcon;

import java.io.File;

public class AddFileController {

    @FXML
    private TextField filePathField;

    @FXML
    private FontIcon loadingSpinner;

    @FXML
    private VBox loadingSection;

    private RotateTransition spinnerAnimation;

    private final AddFileService addFileService = new AddFileService();

    @FXML
    public void initialize() {
        // Initialisation de l’animation de rotation pour l’icône spinner
        spinnerAnimation = new RotateTransition(Duration.seconds(1), loadingSpinner);
        spinnerAnimation.setByAngle(360);
        spinnerAnimation.setCycleCount(RotateTransition.INDEFINITE);
    }

    @FXML
    protected void onDownloadButtonClick() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.getExtensionFilters().addAll(
                new FileChooser.ExtensionFilter("Fichiers Excel ou PDF", "*.xlsx", "*.xls", "*.xlsm", "*.pdf")
        );
        File file = fileChooser.showOpenDialog(null);

        if (file != null) {
            filePathField.setText(file.getAbsolutePath());
        }
    }

    @FXML
    protected void onAddButtonClick() {
        String filePath = filePathField.getText();

        if (filePath != null && !filePath.isEmpty()) {
            // Show loading section
            loadingSection.setVisible(true);
            spinnerAnimation.play();

            new Thread(() -> {
                try {
                    String result = addFileService.saveFichierVersBD(filePath);
                    Platform.runLater(() -> {
                        // Hide loading and show success
                        loadingSection.setVisible(false);
                        spinnerAnimation.stop();

                        // Show success message (you can add a success dialog or notification)
                        Alert alert = new Alert(Alert.AlertType.INFORMATION);
                        alert.setTitle("Succès");
                        alert.setHeaderText("Fichier ajouté avec succès");
                        alert.setContentText(result);
                        alert.showAndWait();

                        // Clear the file path
                        filePathField.clear();
                    });
                } catch (Exception e) {
                    e.printStackTrace();
                    String msg = e.getMessage() != null ? e.getMessage() : "Erreur inconnue.";
                    Platform.runLater(() -> {
                        // Hide loading and show error
                        loadingSection.setVisible(false);
                        spinnerAnimation.stop();

                        // Show error message
                        Alert alert = new Alert(Alert.AlertType.ERROR);
                        alert.setTitle("Erreur");
                        alert.setHeaderText("Erreur lors de l'ajout du fichier");
                        alert.setContentText(msg);
                        alert.showAndWait();
                    });
                }
            }).start();

        } else {
            // Show warning for no file selected
            Alert alert = new Alert(Alert.AlertType.WARNING);
            alert.setTitle("Attention");
            alert.setHeaderText("Aucun fichier sélectionné");
            alert.setContentText("Veuillez sélectionner un fichier avant de continuer.");
            alert.showAndWait();
        }
    }
}
