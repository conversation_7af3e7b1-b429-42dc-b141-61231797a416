<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.geometry.Insets?>

<?import javafx.collections.FXCollections?>
<?import java.lang.String?>
<BorderPane xmlns="http://javafx.com/javafx"
            xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.RechercheController"
            prefHeight="400.0" prefWidth="600.0"
            stylesheets="@css/recherche.css">

    <top>
        <fx:include source="navbar.fxml"/>
    </top>

    <center>
        <VBox styleClass="page-content" spacing="20" alignment="CENTER">
            <padding>
                <Insets top="20" right="20" bottom="20" left="20"/>
            </padding>
            <HBox spacing="10" alignment="CENTER">
                <TextField fx:id="searchField" promptText="Rechercher par description" styleClass="text-field"/>
                <ComboBox fx:id="typeComboBox" promptText="Type" styleClass="combo-box">
                <items>
                        <FXCollections fx:factory="observableArrayList">
                            <String fx:value="Tout"/>
                            <String fx:value="Proto"/>
                            <String fx:value="Série"/>
                        </FXCollections>
                    </items>
                </ComboBox>
                <Button text="Rechercher" onAction="#onSearchClicked"/>
            </HBox>

            <Label fx:id="noResultLabel" text="Aucun résultat trouvé." visible="false" style="-fx-text-fill: red;" />

            <StackPane prefHeight="300" prefWidth="400">
                <TableView fx:id="resultTable" styleClass="results-table" >
                    <columns>
                        <TableColumn fx:id="nomColumn" text="Nomenclature"/>
                        <TableColumn fx:id="decoupageColumn" text="Dec. PSA"/>
                        <TableColumn fx:id="protoColumn" text="Prix unitaire proto (€)" prefWidth="100"/>
                        <TableColumn fx:id="serieColumn" text="Prix unitaire série (€)" prefWidth="100"/>
                        <TableColumn fx:id="nomFichierColumn" text="Nom du fichier" prefWidth="200"/>
                    </columns>
                </TableView>

                <TableView fx:id="pdfResultTable" styleClass="results-table">
                    <columns>
                        <TableColumn fx:id="pdfRefColumn" text="Référence"/>
                        <TableColumn fx:id="pdfNomFichierColumn" text="Nom du fichier"/>
                        <TableColumn fx:id="pdfActionColumn" text="Télécharger"/>
                    </columns>
                </TableView>

                <TableView fx:id="pdfManuelResultTable" styleClass="results-table">
                    <columns>
                        <TableColumn fx:id="pdfManuelRefColumn" text="Référence"/>
                        <TableColumn fx:id="pdfManuelDesignationColumn" text="Désignation"/>
                        <TableColumn fx:id="pdfManuelPrixColumn" text="Prix (€)"/>
                        <TableColumn fx:id="pdfManuelNomFichierColumn" text="Nom du fichier"/>

                    </columns>
                </TableView>




            </StackPane>

            <Label fx:id="loadingLabel" text="Chargement en cours..." visible="false" style="-fx-text-fill: #555555; -fx-font-style: italic;"/>

        </VBox>
    </center>
</BorderPane>
