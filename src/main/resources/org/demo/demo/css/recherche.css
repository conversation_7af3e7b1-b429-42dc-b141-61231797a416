.page-content {
     -fx-background-color: linear-gradient(to bottom right, #e3f2fd, #ede7f6);
    -fx-font-family: "Segoe UI", sans-serif;
    -fx-font-size: 14px;
}

.page-content .button {
    -fx-background-color: #00AEEF;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-padding: 8px 24px;
    -fx-background-radius: 8px;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 3, 0.1, 0, 2);
    -fx-transition: all 0.3s ease;
}

.page-content .button:hover {
    -fx-background-color: #0072BC;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0.2, 0, 2);
}

.text-field, .combo-box {
    -fx-background-radius: 5px;
    -fx-padding: 6px;
    -fx-font-size: 13px;
}

.label {
    -fx-font-size: 13px;
}

/* Table Styles */
.results-table {
    -fx-background-color: white;
    -fx-border-color: #ccc;
    -fx-border-radius: 5px;
    -fx-table-cell-border-color: #ddd;
    -fx-padding: 5px;
    -fx-selection-bar: #00AEEF;
    -fx-selection-bar-non-focused: #ccefff;
}

.results-table .column-header {
    -fx-background-color: #E0E0E0;
    -fx-font-weight: bold;
    -fx-border-color: transparent transparent #ccc transparent;
}

.results-table .table-row-cell:filled:selected {
    -fx-background-color: #ccefff;
}

.results-table .table-row-cell:odd {
    -fx-background-color: #f7f7f7;
}

.results-table .table-row-cell:hover {
    -fx-background-color: #e9f5fc;
}

.table-cell {
    -fx-text-overrun: ellipsis; /* ou clip */
}


