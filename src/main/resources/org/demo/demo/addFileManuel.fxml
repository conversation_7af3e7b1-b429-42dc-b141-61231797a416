<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.Circle?>

<BorderPane xmlns="http://javafx.com/javafx"
            xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.AddFileManuelController"
            stylesheets="@css/addFileManuel.css">

    <!-- Barre de navigation -->
    <top>
        <fx:include source="navbar.fxml"/>
    </top>

    <!-- Contenu principal -->
    <center>
        <AnchorPane styleClass="background-pane">
            <VBox alignment="CENTER" spacing="40" styleClass="center-container"
                  AnchorPane.topAnchor="0" AnchorPane.bottomAnchor="0"
                  AnchorPane.leftAnchor="0" AnchorPane.rightAnchor="0">

                <Region VBox.vgrow="ALWAYS"/>

                <!-- StackPane pour effet visuel -->
                <StackPane maxWidth="700" prefWidth="600" styleClass="stack-container">

                    <!-- Bulles décoratives -->
                    <Circle fx:id="bubble1" radius="150" styleClass="bubble bubble-blue"/>
                    <Circle fx:id="bubble2" radius="100" styleClass="bubble bubble-light"/>

                    <!-- Carte centrale avec formulaire -->
                    <VBox styleClass="card" spacing="25" alignment="CENTER">

                        <Label text="Ajout manuel d'un produit PDF" styleClass="card-title"/>

                        <TextField fx:id="refField" promptText="Référence" maxWidth="400"/>
                        <TextField fx:id="prixField" promptText="Prix" maxWidth="400"/>
                        <TextField fx:id="designationField" promptText="Désignation" maxWidth="400"/>
                        <TextField fx:id="nomFichierField" promptText="Nom du fichier PDF" maxWidth="400"/>

                        <Button fx:id="enregistrerButton" text="Enregistrer" maxWidth="200"/>
                    </VBox>
                </StackPane>

                <Region VBox.vgrow="ALWAYS"/>
            </VBox>
        </AnchorPane>
    </center>
</BorderPane>
