package org.demo.demo.controller;

import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.scene.control.*;
import javafx.collections.FXCollections;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.util.Callback;
import org.demo.demo.entities.ProduitExcel;
import org.demo.demo.entities.PdfExtrait;
import org.demo.demo.entities.ProduitPdfManuel;
import org.demo.demo.services.RechercheService;

import java.awt.Desktop;
import java.io.File;
import java.util.List;

public class RechercheController {

    @FXML
    private TextField searchField;

    @FXML
    private TableView<ProduitExcel> resultTable;

    @FXML
    private TableColumn<ProduitExcel, String> nomColumn;

    @FXML
    private TableColumn<ProduitExcel, String> decoupageColumn;

    @FXML
    private TableColumn<ProduitExcel, String> nomFichierColumn;

    @FXML
    private ComboBox<String> typeComboBox;

    @FXML
    private TableColumn<ProduitExcel, Double> protoColumn;

    @FXML
    private TableColumn<ProduitExcel, Double> serieColumn;

    // Table PDF
    @FXML
    private TableView<PdfExtrait> pdfResultTable;

    @FXML
    private TableColumn<PdfExtrait, String> pdfRefColumn; // changé en String

    @FXML
    private TableColumn<PdfExtrait, String> pdfNomFichierColumn;

    @FXML
    private TableColumn<PdfExtrait, Void> pdfActionColumn;

    @FXML
    private Label loadingLabel;

    @FXML
    private Label noResultLabel;

    private RechercheService recherchService = new RechercheService();

    //manuel

    @FXML
    private TableView<ProduitPdfManuel> pdfManuelResultTable;

    @FXML
    private TableColumn<ProduitPdfManuel, String> pdfManuelRefColumn;

    @FXML
    private TableColumn<ProduitPdfManuel, String> pdfManuelDesignationColumn;

    @FXML
    private TableColumn<ProduitPdfManuel, Double> pdfManuelPrixColumn;

    @FXML
    private TableColumn<ProduitPdfManuel, String> pdfManuelNomFichierColumn;

    @FXML
    public void initialize() {
        // Colonnes Excel
        nomColumn.setCellValueFactory(new PropertyValueFactory<>("nom"));
        decoupageColumn.setCellValueFactory(new PropertyValueFactory<>("decoupage"));
        nomFichierColumn.setCellValueFactory(new PropertyValueFactory<>("nomFichier"));
        protoColumn.setCellValueFactory(new PropertyValueFactory<>("prixUnitaireProto"));
        serieColumn.setCellValueFactory(new PropertyValueFactory<>("prixUnitaireSerie"));
        resultTable.setVisible(false);

        pdfRefColumn.setCellValueFactory(cellData ->
                new javafx.beans.property.SimpleStringProperty(searchField.getText())
        );

        pdfNomFichierColumn.setCellValueFactory(cellData -> {
            PdfExtrait extrait = cellData.getValue();
            String nomFichier = "";
            if (extrait.getFichier() != null) {
                nomFichier = extrait.getFichier().getNom_fichier();
            }
            return new javafx.beans.property.SimpleStringProperty(nomFichier);
        });

        addDownloadButtonToTable();

        pdfResultTable.setVisible(false);

        typeComboBox.setValue("Tout");

        resultTable.widthProperty().addListener((obs, oldVal, newVal) -> ajusterLargeurColonnes());
        pdfResultTable.widthProperty().addListener((obs, oldVal, newVal) -> ajusterLargeurColonnesPdf());

        //manuel
        pdfManuelRefColumn.setCellValueFactory(new PropertyValueFactory<>("ref"));
        pdfManuelDesignationColumn.setCellValueFactory(new PropertyValueFactory<>("designation"));
        pdfManuelPrixColumn.setCellValueFactory(new PropertyValueFactory<>("prix"));
        pdfManuelNomFichierColumn.setCellValueFactory(cellData -> {
            ProduitPdfManuel produit = cellData.getValue();
            String nomFichier = "";
            if (produit.getFichier() != null) {
                nomFichier = produit.getFichier().getNom_fichier();
            }
            return new javafx.beans.property.SimpleStringProperty(nomFichier);
        });

        pdfManuelNomFichierColumn.setPrefWidth(150);
        pdfManuelResultTable.setMinHeight(150);
        pdfManuelResultTable.setVisible(false);

        pdfManuelResultTable.widthProperty().addListener((obs, oldVal, newVal) -> ajusterLargeurColonnesManuel());
    }

    private void ajusterLargeurColonnes() {
        long colonnesVisibles = resultTable.getColumns().stream().filter(TableColumn::isVisible).count();
        if (colonnesVisibles == 0) return;
        double largeurColonne = resultTable.getWidth() / colonnesVisibles;
        for (TableColumn<?, ?> col : resultTable.getColumns()) {
            if (col.isVisible()) col.setPrefWidth(largeurColonne);
        }
    }

    private void ajusterLargeurColonnesPdf() {
        long colonnesVisibles = pdfResultTable.getColumns().stream().filter(TableColumn::isVisible).count();
        if (colonnesVisibles == 0) return;
        double largeurColonne = pdfResultTable.getWidth() / colonnesVisibles;
        for (TableColumn<?, ?> col : pdfResultTable.getColumns()) {
            if (col.isVisible()) col.setPrefWidth(largeurColonne);
        }
    }
    private void ajusterLargeurColonnesManuel() {
        long colonnesVisibles = pdfManuelResultTable.getColumns().stream().filter(TableColumn::isVisible).count();
        if (colonnesVisibles == 0) return;
        double largeurColonne = pdfManuelResultTable.getWidth() / colonnesVisibles;
        for (TableColumn<?, ?> col : pdfManuelResultTable.getColumns()) {
            if (col.isVisible()) col.setPrefWidth(largeurColonne);
        }
    }

    private void addDownloadButtonToTable() {
        pdfActionColumn.setCellFactory(new Callback<>() {
            @Override
            public TableCell<PdfExtrait, Void> call(final TableColumn<PdfExtrait, Void> param) {
                return new TableCell<>() {
                    private final Button btn = new Button("Télécharger");

                    {
                        btn.setOnAction(event -> {
                            PdfExtrait extrait = getTableView().getItems().get(getIndex());
                            if (extrait.getFichier() != null) {
                                // Récupérer le chemin complet depuis la base
                                String cheminComplet = extrait.getFichier().getPath();

                                if (cheminComplet != null && !cheminComplet.isEmpty()) {
                                    File file = new File(cheminComplet);
                                    if (file.exists()) {
                                        try {
                                            Desktop.getDesktop().open(file);
                                        } catch (Exception e) {
                                            e.printStackTrace();
                                            showAlert("Erreur", "Impossible d'ouvrir le fichier PDF.");
                                        }
                                    } else {
                                        showAlert("Fichier introuvable", "Le fichier est introuvable : " + cheminComplet);
                                    }
                                } else {
                                    showAlert("Chemin manquant", "Le chemin du fichier n'est pas défini.");
                                }
                            } else {
                                showAlert("Fichier manquant", "Aucun fichier associé à cet extrait.");
                            }
                        });
                    }

                    @Override
                    protected void updateItem(Void item, boolean empty) {
                        super.updateItem(item, empty);
                        setGraphic(empty ? null : btn);
                    }
                };
            }
        });
    }


    private void showAlert(String titre, String message) {
        Alert alert = new Alert(Alert.AlertType.ERROR);
        alert.setTitle(titre);
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }

    @FXML
    private void onSearchClicked() {
        String keyword = searchField.getText();
        String type = typeComboBox.getValue();

        loadingLabel.setVisible(true);

        Task<Void> task = new Task<>() {
            List<ProduitExcel> results;
            List<PdfExtrait> pdfResults;
            //manuel
            List<ProduitPdfManuel> pdfManuelResults;


            @Override
            protected Void call() {
                results = recherchService.rechercherProduitsParDescription(keyword);
                pdfResults = recherchService.rechercherDansExtraitsPDF(keyword);
                //manuel
                pdfManuelResults = recherchService.rechercherProduitsPdfDepuisBase(keyword);

                return null;
            }

            @Override
            protected void succeeded() {
                resultTable.setItems(FXCollections.observableArrayList(results));
                pdfResultTable.setItems(FXCollections.observableArrayList(pdfResults));
                pdfManuelResultTable.setItems(FXCollections.observableArrayList(pdfManuelResults));


                if (!results.isEmpty()) {
                    resultTable.setVisible(true);
                    pdfResultTable.setVisible(false);
                    //manuel
                    pdfManuelResultTable.setVisible(false);

                    noResultLabel.setVisible(false);

                    switch (type) {
                        case "Proto" -> {
                            protoColumn.setVisible(true);
                            serieColumn.setVisible(false);
                        }
                        case "Série" -> {
                            protoColumn.setVisible(false);
                            serieColumn.setVisible(true);
                        }
                        default -> {
                            protoColumn.setVisible(true);
                            serieColumn.setVisible(true);
                        }
                    }

                    ajusterLargeurColonnes();
                } else if (!pdfResults.isEmpty()) {
                    resultTable.setVisible(false);
                    pdfResultTable.setVisible(true);
                    //manuel
                    pdfManuelResultTable.setVisible(false);

                    noResultLabel.setVisible(false);
                    ajusterLargeurColonnesPdf();

                    // Forcer le rafraîchissement pour que la colonne référence mette à jour sa valeur
                    pdfResultTable.refresh();

                } else if (!pdfManuelResults.isEmpty()) {
                    resultTable.setVisible(false);
                    pdfResultTable.setVisible(false);
                    //manuel
                    pdfManuelResultTable.setVisible(true);
                    noResultLabel.setVisible(false); // Masquer le message
                    ajusterLargeurColonnesManuel();
                }else {
                    resultTable.setVisible(false);
                    pdfResultTable.setVisible(false);
                    pdfManuelResultTable.setVisible(false);

                    noResultLabel.setVisible(true);
                }

                loadingLabel.setVisible(false);
            }

            @Override
            protected void failed() {
                loadingLabel.setVisible(false);
                showAlert("Erreur", "Une erreur est survenue lors de la recherche.");
            }
        };

        new Thread(task).start();
    }
}
