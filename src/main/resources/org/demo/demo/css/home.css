/* ======= Creative Home Design ======= */

/* Global Styles */
/* Fix pour les icônes FontIcon */
.font-icon {
    -fx-background-color: transparent;
    -fx-background-radius: 0;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 0;
}
.scroll-pane {
    -fx-background-color: transparent;
    -fx-background: transparent;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

.scroll-pane .scroll-bar {
    -fx-opacity: 0.3;
}

.main-container {
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #f8fafc, #e2e8f0);
    -fx-spacing: 0;
}

/* ======= Creative Hero Section ======= */
.creative-hero {
    -fx-min-height: 280;
    -fx-padding: 40 30;
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #667eea, #764ba2);
}

/* Floating Animation Elements */
.floating-element {
    -fx-opacity: 0.15;
    -fx-fill: white;
}

.floating-1 {
    -fx-translate-x: -300;
    -fx-translate-y: -150;
}

.floating-2 {
    -fx-translate-x: 280;
    -fx-translate-y: -100;
}

.floating-3 {
    -fx-translate-x: -250;
    -fx-translate-y: 120;
}

.floating-4 {
    -fx-translate-x: 320;
    -fx-translate-y: 80;
}

.floating-5 {
    -fx-translate-x: 0;
    -fx-translate-y: -200;
}

/* Geometric Shapes */
.geometric-shape {
    -fx-opacity: 0.1;
    -fx-fill: white;
}

.triangle-1 {
    -fx-translate-x: -200;
    -fx-translate-y: 50;
    -fx-rotate: 45;
}

.square-1 {
    -fx-translate-x: 200;
    -fx-translate-y: -80;
    -fx-rotate: 30;
    -fx-arc-width: 10;
    -fx-arc-height: 10;
}

.square-2 {
    -fx-translate-x: -150;
    -fx-translate-y: -120;
    -fx-rotate: -20;
    -fx-arc-width: 8;
    -fx-arc-height: 8;
}

/* Hero Content */
.hero-main-content {
    -fx-max-width: 700;
}

.title-container {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 3, 0, 0, 2);
}

.creative-title {
    -fx-font-size: 32px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 2, 0, 0, 1);
}

.creative-subtitle {
    -fx-font-size: 16px;
    -fx-font-weight: 300;
    -fx-text-fill: rgba(255, 255, 255, 0.9);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.title-underline {
    -fx-fill: linear-gradient(to right, #FFD700, #FFA500);
    -fx-arc-width: 2;
    -fx-arc-height: 2;
}

.hero-tagline {
    -fx-font-size: 18px;
    -fx-font-weight: 600;
    -fx-text-fill: #FFD700;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.hero-description {
    -fx-font-size: 16px;
    -fx-text-fill: rgba(255, 255, 255, 0.85);
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-line-spacing: 2;
}

/* Creative Buttons */
.creative-primary-btn {
    -fx-background-color: linear-gradient(to bottom, #FF6B6B, #FF5252);
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-padding: 15 25;
    -fx-background-radius: 30;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(255, 107, 107, 0.4), 10, 0, 0, 4);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.creative-primary-btn:hover {
    -fx-background-color: linear-gradient(to bottom, #FF5252, #FF4444);
    -fx-effect: dropshadow(gaussian, rgba(255, 107, 107, 0.6), 15, 0, 0, 6);
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
}

.creative-secondary-btn {
    -fx-background-color: linear-gradient(to bottom, #4ECDC4, #44A08D);
    -fx-text-fill: white;
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-padding: 15 25;
    -fx-background-radius: 30;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(78, 205, 196, 0.4), 10, 0, 0, 4);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.creative-secondary-btn:hover {
    -fx-background-color: linear-gradient(to bottom, #44A08D, #3A8B7A);
    -fx-effect: dropshadow(gaussian, rgba(78, 205, 196, 0.6), 15, 0, 0, 6);
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
}

.btn-icon {
    -fx-icon-size: 18;
    -fx-icon-color: white;
}

.btn-text {
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

/* ======= Creative Features Section ======= */
.creative-features-section {
    -fx-padding: 50 30;
    -fx-background-color: white;
    -fx-alignment: center;
}

.section-header {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 2, 0, 0, 1);
}

.creative-section-title {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2C3E50;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.creative-section-subtitle {
    -fx-font-size: 14px;
    -fx-text-fill: #5A6C7D;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.section-underline {
    -fx-fill: linear-gradient(to right, #FF6B6B, #4ECDC4);
    -fx-arc-width: 1.5;
    -fx-arc-height: 1.5;
}

/* Modern Feature Cards */
.features-grid {
    -fx-max-width: 800;
    -fx-alignment: center;
}

.modern-feature-card {
    -fx-background-color: white;
    -fx-padding: 25;
    -fx-background-radius: 15;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 15, 0.2, 0, 3);
    -fx-max-width: 250;
    -fx-min-width: 250;
    -fx-min-height: 280;
}

.modern-feature-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 25, 0.3, 0, 8);
    -fx-translate-y: -5;
}

.modern-icon-container {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 8, 0, 0, 2);
}

.modern-icon-bg {
    -fx-opacity: 0.9;
}

.modern-bg-1 {
    -fx-fill: linear-gradient(to bottom, #4A90E2, #6BB6FF);
}

.modern-bg-2 {
    -fx-fill: linear-gradient(to bottom, #5DADE2, #85C1E9);
}

.modern-bg-3 {
    -fx-fill: linear-gradient(to bottom, #3498DB, #5DADE2);
}

.modern-icon {
    -fx-icon-size: 24;
    -fx-icon-color: #FF6B6B;
    -fx-background-color: transparent;
    -fx-background-radius: 0;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 0;
}

/* Couleurs spécifiques pour chaque icône */
.icon-blue-1 {
    -fx-icon-color: #4A90E2;
}

.icon-blue-2 {
    -fx-icon-color: #5DADE2;
}

.icon-blue-3 {
    -fx-icon-color: #3498DB;
}

.modern-feature-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #2C3E50;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-feature-description {
    -fx-font-size: 13px;
    -fx-text-fill: #5A6C7D;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-line-spacing: 1.5;
}

.modern-feature-button {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa, #e9ecef);
    -fx-text-fill: #2C3E50;
    -fx-font-size: 14px;
    -fx-font-weight: 600;
    -fx-padding: 12 20;
    -fx-background-radius: 20;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1;
    -fx-border-radius: 20;
    -fx-cursor: hand;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-feature-button:hover {
    -fx-background-color: linear-gradient(to bottom, #FF6B6B, #FF5252);
    -fx-text-fill: white;
    -fx-border-color: #FF6B6B;
}

/* ======= Creative Stats Dashboard ======= */
.stats-dashboard {
    -fx-padding: 40 30;
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #f8f9fa, #e9ecef);
    -fx-min-height: 150;
}

.stats-pattern {
    -fx-background-color: transparent;
}

.stats-container {
    -fx-max-width: 650;
    -fx-alignment: center;
}

.stat-card {
    -fx-background-color: white;
    -fx-padding: 18;
    -fx-background-radius: 12;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 10, 0.2, 0, 2);
    -fx-min-width: 110;
    -fx-max-width: 110;
}

.stat-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 20, 0.3, 0, 8);
    -fx-translate-y: -3;
}

.stat-icon-container {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 6, 0, 0, 2);
}

.stat-icon-bg {
    -fx-opacity: 1.0;
}

.stat-bg-1 {
    -fx-fill: linear-gradient(to bottom, #4A90E2, #6BB6FF);
}

.stat-bg-2 {
    -fx-fill: linear-gradient(to bottom, #5DADE2, #85C1E9);
}

.stat-bg-3 {
    -fx-fill: linear-gradient(to bottom, #3498DB, #5DADE2);
}

.stat-bg-4 {
    -fx-fill: linear-gradient(to bottom, #2E86C1, #4A90E2);
}

.stat-icon {
    -fx-icon-size: 16;
    -fx-icon-color: white;
    -fx-background-color: transparent;
    -fx-background-radius: 0;
    -fx-border-color: transparent;
    -fx-border-width: 0;
    -fx-padding: 0;
}

.creative-stat-number {
    -fx-font-size: 22px;
    -fx-font-weight: bold;
    -fx-text-fill: #2C3E50;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.creative-stat-label {
    -fx-font-size: 11px;
    -fx-text-fill: #5A6C7D;
    -fx-font-weight: 600;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}
