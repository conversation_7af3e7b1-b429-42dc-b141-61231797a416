<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.layout.*?>
<?import javafx.scene.control.*?>
<?import javafx.scene.shape.*?>
<?import javafx.geometry.Insets?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<BorderPane xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.AddFileController"
            stylesheets="@css/addfile.css">

  <top>
    <fx:include source="navbar.fxml"/>
  </top>

  <center>
    <ScrollPane fitToWidth="true" fitToHeight="true" styleClass="scroll-pane">
      <VBox styleClass="main-container">

        <!-- Hero Section with Decorative Elements -->
        <StackPane styleClass="hero-section">
          <!-- Animated Background Elements -->
          <Circle fx:id="decorCircle1" radius="60" styleClass="floating-element floating-1"/>
          <Circle fx:id="decorCircle2" radius="40" styleClass="floating-element floating-2"/>
          <Circle fx:id="decorCircle3" radius="50" styleClass="floating-element floating-3"/>

          <!-- Geometric Shapes -->
          <Rectangle fx:id="decorRect1" width="80" height="80" styleClass="geometric-shape rect-1"/>
          <Polygon fx:id="decorTriangle1" styleClass="geometric-shape triangle-1" points="0,0,40,0,20,35"/>

          <!-- Main Content -->
          <VBox alignment="CENTER" spacing="20" styleClass="hero-content">
            <VBox alignment="CENTER" spacing="10" styleClass="title-section">
              <FontIcon iconLiteral="bi-cloud-upload" styleClass="hero-icon"/>
              <Label text="📁 Gestion des Fichiers" styleClass="hero-title"/>
              <Label text="Ajoutez facilement vos fichiers à la base de données" styleClass="hero-subtitle"/>
              <Rectangle width="80" height="3" styleClass="title-underline"/>
            </VBox>
          </VBox>
        </StackPane>

        <!-- Main Content Card -->
        <VBox styleClass="content-section" spacing="40" alignment="CENTER">
          <padding>
            <Insets top="40" right="40" bottom="40" left="40"/>
          </padding>

          <!-- Upload Card -->
          <StackPane styleClass="upload-card-container">
            <!-- Decorative background circles -->
            <Circle radius="120" styleClass="card-bg-circle circle-1"/>
            <Circle radius="80" styleClass="card-bg-circle circle-2"/>

            <!-- Main Upload Card -->
            <VBox styleClass="upload-card" spacing="30" alignment="CENTER">
              <VBox alignment="CENTER" spacing="15" styleClass="upload-icon-section">
                <StackPane styleClass="upload-icon-container">
                  <Circle radius="50" styleClass="upload-icon-bg"/>
                  <FontIcon iconLiteral="bi-file-earmark-arrow-up" styleClass="upload-main-icon"/>
                </StackPane>
                <Label text="Sélectionnez votre fichier" styleClass="upload-title"/>
                <Label text="Formats supportés: Excel (.xlsx, .xls), PDF (.pdf)" styleClass="upload-description"/>
              </VBox>

              <!-- File Selection Area -->
              <VBox spacing="20" alignment="CENTER" styleClass="file-selection-area">
                <HBox spacing="15" alignment="CENTER" styleClass="file-input-container">
                  <TextField fx:id="filePathField" promptText="Aucun fichier sélectionné..."
                           editable="false" styleClass="modern-file-field"/>
                  <Button onAction="#onDownloadButtonClick" styleClass="browse-button">
                    <graphic>
                      <HBox spacing="8" alignment="CENTER">
                        <FontIcon iconLiteral="bi-folder2-open" styleClass="browse-icon"/>
                        <Label text="Parcourir" styleClass="browse-text"/>
                      </HBox>
                    </graphic>
                  </Button>
                </HBox>

                <!-- Action Buttons -->
                <HBox spacing="20" alignment="CENTER" styleClass="action-buttons">
                  <Button text="Annuler" styleClass="secondary-button">
                    <graphic>
                      <HBox spacing="8" alignment="CENTER">
                        <FontIcon iconLiteral="bi-x-circle" styleClass="btn-icon"/>
                        <Label text="Annuler" styleClass="btn-text"/>
                      </HBox>
                    </graphic>
                  </Button>
                  <Button onAction="#onAddButtonClick" styleClass="primary-button">
                    <graphic>
                      <HBox spacing="8" alignment="CENTER">
                        <FontIcon iconLiteral="bi-plus-circle" styleClass="btn-icon"/>
                        <Label text="Ajouter le Fichier" styleClass="btn-text"/>
                      </HBox>
                    </graphic>
                  </Button>
                </HBox>
              </VBox>
            </VBox>
          </StackPane>

          <!-- Status and Loading Section -->
          <VBox alignment="CENTER" spacing="20" styleClass="status-section">
            <Label fx:id="Text" text="Ajoutez un fichier à votre base de données" styleClass="status-label"/>

            <!-- Loading Spinner -->
            <StackPane styleClass="loading-container">
              <Circle radius="25" styleClass="loading-bg"/>
              <FontIcon fx:id="loadingSpinner" iconLiteral="bi-arrow-repeat" styleClass="loading-spinner" visible="false"/>
            </StackPane>
          </VBox>

        </VBox>

      </VBox>
    </ScrollPane>
  </center>

</BorderPane>
