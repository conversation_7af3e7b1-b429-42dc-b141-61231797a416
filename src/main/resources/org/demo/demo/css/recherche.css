/* ======= Modern Search Design ======= */

/* Global Styles */
.scroll-pane {
    -fx-background-color: transparent;
    -fx-background: transparent;
}

.scroll-pane .viewport {
    -fx-background-color: transparent;
}

.scroll-pane .scroll-bar {
    -fx-opacity: 0.3;
}

.main-container {
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #f8fafc, #e2e8f0);
    -fx-spacing: 0;
}

/* ======= Search Hero Section ======= */
.search-hero-section {
    -fx-min-height: 180;
    -fx-padding: 25;
    -fx-background-color: linear-gradient(from 0% 0% to 100% 100%, #4A90E2, #6BB6FF);
}

/* Floating Animation Elements */
.floating-element {
    -fx-opacity: 0.15;
    -fx-fill: white;
}

.floating-1 {
    -fx-translate-x: -180;
    -fx-translate-y: -70;
}

.floating-2 {
    -fx-translate-x: 160;
    -fx-translate-y: -50;
}

.floating-3 {
    -fx-translate-x: -130;
    -fx-translate-y: 60;
}

/* Geometric Shapes */
.geometric-shape {
    -fx-opacity: 0.1;
    -fx-fill: white;
}

.rect-1 {
    -fx-translate-x: 140;
    -fx-translate-y: -35;
    -fx-rotate: 20;
    -fx-arc-width: 12;
    -fx-arc-height: 12;
}

.triangle-1 {
    -fx-translate-x: -110;
    -fx-translate-y: 35;
    -fx-rotate: 25;
}

/* Search Hero Content */
.search-hero-content {
    -fx-max-width: 500;
}

.search-title-section {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.2), 3, 0, 0, 2);
}

.search-hero-icon {
    -fx-icon-size: 42;
    -fx-icon-color: white;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 2, 0, 0, 1);
}

.search-hero-title {
    -fx-font-size: 26px;
    -fx-font-weight: bold;
    -fx-text-fill: white;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.3), 2, 0, 0, 1);
}

.search-hero-subtitle {
    -fx-font-size: 15px;
    -fx-font-weight: 300;
    -fx-text-fill: rgba(255, 255, 255, 0.9);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.search-title-underline {
    -fx-fill: linear-gradient(to right, #FFD700, #FFA500);
    -fx-arc-width: 1.5;
    -fx-arc-height: 1.5;
}

/* ======= Search Content Section ======= */
.search-content-section {
    -fx-background-color: white;
    -fx-alignment: center;
}

/* ======= Search Card ======= */
.search-card-container {
    -fx-max-width: 550;
    -fx-min-width: 550;
}

.search-bg-circle {
    -fx-opacity: 0.05;
}

.circle-1 {
    -fx-fill: #4A90E2;
    -fx-translate-x: -90;
    -fx-translate-y: -40;
}

.circle-2 {
    -fx-fill: #6BB6FF;
    -fx-translate-x: 100;
    -fx-translate-y: 50;
}

.search-card {
    -fx-background-color: white;
    -fx-padding: 35;
    -fx-background-radius: 18;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.15), 18, 0.3, 0, 6);
    -fx-max-width: 480;
    -fx-min-width: 480;
}

.search-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.25), 25, 0.4, 0, 10);
    -fx-translate-y: -2;
}

/* Search Icon Section */
.search-icon-container {
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 8, 0, 0, 2);
}

.search-icon-bg {
    -fx-fill: linear-gradient(to bottom, #4A90E2, #6BB6FF);
    -fx-opacity: 0.1;
}

.search-main-icon {
    -fx-icon-size: 32;
    -fx-icon-color: #4A90E2;
}

.search-title {
    -fx-font-size: 20px;
    -fx-font-weight: bold;
    -fx-text-fill: #2C3E50;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.search-description {
    -fx-font-size: 13px;
    -fx-text-fill: #5A6C7D;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

/* Search Input Area */
.search-input-area {
    -fx-max-width: 420;
}

.search-input-container {
    -fx-max-width: 420;
}

.modern-search-field {
    -fx-pref-width: 250;
    -fx-font-size: 14px;
    -fx-border-radius: 10;
    -fx-background-radius: 10;
    -fx-border-color: #E1E8ED;
    -fx-border-width: 2;
    -fx-padding: 10 14;
    -fx-background-color: #F8F9FA;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-search-field:focused {
    -fx-border-color: #4A90E2;
    -fx-background-color: white;
}

.modern-combo-box {
    -fx-pref-width: 140;
    -fx-font-size: 14px;
    -fx-border-radius: 10;
    -fx-background-radius: 10;
    -fx-border-color: #E1E8ED;
    -fx-border-width: 2;
    -fx-padding: 10 14;
    -fx-background-color: #F8F9FA;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-combo-box:focused {
    -fx-border-color: #4A90E2;
    -fx-background-color: white;
}

/* Search Button */
.search-primary-button {
    -fx-background-color: linear-gradient(to bottom, #4A90E2, #6BB6FF);
    -fx-text-fill: white;
    -fx-font-size: 15px;
    -fx-font-weight: 600;
    -fx-padding: 12 22;
    -fx-background-radius: 20;
    -fx-cursor: hand;
    -fx-effect: dropshadow(gaussian, rgba(74, 144, 226, 0.4), 8, 0, 0, 3);
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.search-primary-button:hover {
    -fx-background-color: linear-gradient(to bottom, #6BB6FF, #8AC4FF);
    -fx-effect: dropshadow(gaussian, rgba(74, 144, 226, 0.6), 12, 0, 0, 5);
    -fx-scale-y: 1.05;
    -fx-scale-x: 1.05;
}

.search-btn-icon {
    -fx-icon-size: 16;
    -fx-icon-color: white;
}

.search-btn-text {
    -fx-text-fill: white;
    -fx-font-weight: 600;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

/* ======= Results Section ======= */
.results-section {
    -fx-max-width: 750;
}

.status-messages {
    -fx-max-width: 400;
}

.no-result-message {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #E74C3C;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.loading-message {
    -fx-font-size: 16px;
    -fx-font-weight: 600;
    -fx-text-fill: #4A90E2;
    -fx-text-alignment: center;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
    -fx-font-style: italic;
}

/* Tables Container */
.tables-container {
    -fx-max-width: 720;
    -fx-min-width: 720;
    -fx-min-height: 300;
}

/* Modern Table Styles */
.modern-results-table {
    -fx-background-color: white;
    -fx-border-color: #E1E8ED;
    -fx-border-radius: 12;
    -fx-background-radius: 12;
    -fx-table-cell-border-color: #F1F3F4;
    -fx-padding: 8;
    -fx-selection-bar: #4A90E2;
    -fx-selection-bar-non-focused: #E3F2FD;
    -fx-effect: dropshadow(gaussian, rgba(0, 0, 0, 0.1), 12, 0.2, 0, 3);
}

.modern-results-table .column-header {
    -fx-background-color: linear-gradient(to bottom, #F8F9FA, #E9ECEF);
    -fx-font-weight: bold;
    -fx-font-size: 13px;
    -fx-text-fill: #2C3E50;
    -fx-border-color: transparent transparent #DEE2E6 transparent;
    -fx-padding: 12 8;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-results-table .table-row-cell {
    -fx-padding: 8;
    -fx-font-size: 13px;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}

.modern-results-table .table-row-cell:filled:selected {
    -fx-background-color: #E3F2FD;
    -fx-text-fill: #1976D2;
}

.modern-results-table .table-row-cell:odd {
    -fx-background-color: #FAFBFC;
}

.modern-results-table .table-row-cell:hover {
    -fx-background-color: #E8F4FD;
}

.modern-results-table .table-cell {
    -fx-text-overrun: ellipsis;
    -fx-padding: 8;
    -fx-font-family: "Segoe UI", "Helvetica Neue", Arial, sans-serif;
}


