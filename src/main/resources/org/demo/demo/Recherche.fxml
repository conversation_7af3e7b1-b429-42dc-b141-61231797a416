<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.shape.*?>
<?import javafx.geometry.Insets?>
<?import org.kordamp.ikonli.javafx.FontIcon?>

<?import javafx.collections.FXCollections?>
<?import java.lang.String?>
<BorderPane xmlns="http://javafx.com/javafx"
            xmlns:fx="http://javafx.com/fxml"
            fx:controller="org.demo.demo.controller.RechercheController"
            prefHeight="400.0" prefWidth="600.0"
            stylesheets="@css/recherche.css">

    <top>
        <fx:include source="navbar.fxml"/>
    </top>

    <center>
        <ScrollPane fitToWidth="true" fitToHeight="true" styleClass="scroll-pane">
            <VBox styleClass="main-container">

                <!-- Hero Section with Decorative Elements -->
                <StackPane styleClass="search-hero-section">
                    <!-- Animated Background Elements -->
                    <Circle fx:id="searchCircle1" radius="50" styleClass="floating-element floating-1"/>
                    <Circle fx:id="searchCircle2" radius="35" styleClass="floating-element floating-2"/>
                    <Circle fx:id="searchCircle3" radius="45" styleClass="floating-element floating-3"/>

                    <!-- Geometric Shapes -->
                    <Rectangle fx:id="searchRect1" width="70" height="70" styleClass="geometric-shape rect-1"/>
                    <Polygon fx:id="searchTriangle1" styleClass="geometric-shape triangle-1" points="0,0,35,0,17.5,30"/>

                    <!-- Main Content -->
                    <VBox alignment="CENTER" spacing="15" styleClass="search-hero-content">
                        <VBox alignment="CENTER" spacing="8" styleClass="search-title-section">
                            <FontIcon iconLiteral="bi-search" styleClass="search-hero-icon"/>
                            <Label text="🔍 Recherche Intelligente" styleClass="search-hero-title"/>
                            <Label text="Trouvez rapidement vos données dans la base" styleClass="search-hero-subtitle"/>
                            <Rectangle width="70" height="3" styleClass="search-title-underline"/>
                        </VBox>
                    </VBox>
                </StackPane>

                <!-- Search Content Section -->
                <VBox styleClass="search-content-section" spacing="30" alignment="CENTER">
                    <padding>
                        <Insets top="30" right="30" bottom="30" left="30"/>
                    </padding>

                    <!-- Search Card -->
                    <StackPane styleClass="search-card-container">
                        <!-- Decorative background circles -->
                        <Circle radius="100" styleClass="search-bg-circle circle-1"/>
                        <Circle radius="70" styleClass="search-bg-circle circle-2"/>

                        <!-- Main Search Card -->
                        <VBox styleClass="search-card" spacing="25" alignment="CENTER">
                            <VBox alignment="CENTER" spacing="12" styleClass="search-icon-section">
                                <StackPane styleClass="search-icon-container">
                                    <Circle radius="40" styleClass="search-icon-bg"/>
                                    <FontIcon iconLiteral="bi-database-search" styleClass="search-main-icon"/>
                                </StackPane>
                                <Label text="Recherche avancée" styleClass="search-title"/>
                                <Label text="Utilisez les filtres pour affiner votre recherche" styleClass="search-description"/>
                            </VBox>

                            <!-- Search Input Area -->
                            <VBox spacing="15" alignment="CENTER" styleClass="search-input-area">
                                <HBox spacing="12" alignment="CENTER" styleClass="search-input-container">
                                    <TextField fx:id="searchField" promptText="Rechercher par description..." styleClass="modern-search-field"/>
                                    <ComboBox fx:id="typeComboBox" promptText="Type" styleClass="modern-combo-box">
                                        <items>
                                            <FXCollections fx:factory="observableArrayList">
                                                <String fx:value="Tout"/>
                                                <String fx:value="Proto"/>
                                                <String fx:value="Série"/>
                                            </FXCollections>
                                        </items>
                                    </ComboBox>
                                </HBox>

                                <!-- Search Button -->
                                <Button onAction="#onSearchClicked" styleClass="search-primary-button">
                                    <graphic>
                                        <HBox spacing="8" alignment="CENTER">
                                            <FontIcon iconLiteral="bi-search" styleClass="search-btn-icon"/>
                                            <Label text="Lancer la Recherche" styleClass="search-btn-text"/>
                                        </HBox>
                                    </graphic>
                                </Button>
                            </VBox>
                        </VBox>
                    </StackPane>

                    <!-- Results Section -->
                    <VBox spacing="20" alignment="CENTER" styleClass="results-section">
                        <!-- Status Messages -->
                        <VBox alignment="CENTER" spacing="10" styleClass="status-messages">
                            <Label fx:id="noResultLabel" text="Aucun résultat trouvé." visible="false" styleClass="no-result-message"/>
                            <Label fx:id="loadingLabel" text="Chargement en cours..." visible="false" styleClass="loading-message"/>
                        </VBox>

                        <!-- Results Tables Container -->
                        <StackPane styleClass="tables-container">
                            <TableView fx:id="resultTable" styleClass="modern-results-table">
                                <columns>
                                    <TableColumn fx:id="nomColumn" text="Nomenclature"/>
                                    <TableColumn fx:id="decoupageColumn" text="Dec. PSA"/>
                                    <TableColumn fx:id="protoColumn" text="Prix unitaire proto (€)" prefWidth="120"/>
                                    <TableColumn fx:id="serieColumn" text="Prix unitaire série (€)" prefWidth="120"/>
                                    <TableColumn fx:id="nomFichierColumn" text="Nom du fichier" prefWidth="180"/>
                                </columns>
                            </TableView>

                            <TableView fx:id="pdfResultTable" styleClass="modern-results-table">
                                <columns>
                                    <TableColumn fx:id="pdfRefColumn" text="Référence"/>
                                    <TableColumn fx:id="pdfNomFichierColumn" text="Nom du fichier"/>
                                    <TableColumn fx:id="pdfActionColumn" text="Télécharger"/>
                                </columns>
                            </TableView>

                            <TableView fx:id="pdfManuelResultTable" styleClass="modern-results-table">
                                <columns>
                                    <TableColumn fx:id="pdfManuelRefColumn" text="Référence"/>
                                    <TableColumn fx:id="pdfManuelDesignationColumn" text="Désignation"/>
                                    <TableColumn fx:id="pdfManuelPrixColumn" text="Prix (€)"/>
                                    <TableColumn fx:id="pdfManuelNomFichierColumn" text="Nom du fichier"/>
                                </columns>
                            </TableView>
                        </StackPane>
                    </VBox>

                </VBox>

            </VBox>
        </ScrollPane>
    </center>
</BorderPane>
